/* Product Page Enhancements - Modern styling for FoxyTour products */

/* Smooth animations and transitions */
* {
  scroll-behavior: smooth;
}

/* Enhanced product page container */
.product-with-booking {
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  border-radius: 24px;
  padding: 2rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Modern card-like appearance for product sections */
.product__info-container .accordion {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin: 1rem 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.product__info-container .accordion:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced accordion styling */
.accordion summary {
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.accordion summary:hover {
  background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.08));
}

.accordion .accordion__content {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
}

/* Enhanced form styling */
.product-form__buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.product-form__cart-submit {
  background: linear-gradient(135deg, #00b894, #00a085) !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 1rem 2rem !important;
  color: white !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3) !important;
  min-height: 4.6rem !important;
  flex: 1 !important;
  min-width: 200px !important;
}

.product-form__cart-submit:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4) !important;
  background: linear-gradient(135deg, #00a085, #00b894) !important;
}

.product-form__cart-submit:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Enhanced variant picker styling */
.product-form__input {
  margin: 1.5rem 0;
}

.product-form__input legend {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3436;
}

@media (prefers-color-scheme: dark) {
  .product-form__input legend {
    color: #ddd;
  }
}

/* Enhanced swatch styling */
.color-swatch {
  border-radius: 50% !important;
  border: 3px solid transparent !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.color-swatch:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.color-swatch.selected {
  border-color: #667eea !important;
  transform: scale(1.1) !important;
}

/* Enhanced quantity selector */
.quantity__input {
  border-radius: 8px !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  padding: 0.8rem !important;
  text-align: center !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.quantity__input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  outline: none !important;
}

.quantity__button {
  border-radius: 8px !important;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: #2d3436 !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  padding: 0.8rem !important;
}

.quantity__button:hover {
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1)) !important;
  transform: translateY(-1px) !important;
}

@media (prefers-color-scheme: dark) {
  .quantity__button {
    color: #ddd !important;
  }
}

/* Enhanced share button */
.share-button {
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
  padding: 1rem 1.5rem !important;
  color: #2d3436 !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.share-button:hover {
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1)) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

@media (prefers-color-scheme: dark) {
  .share-button {
    color: #ddd !important;
  }
}

/* Mobile responsiveness */
@media screen and (max-width: 749px) {
  .product-with-booking {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 16px;
  }
  
  .product__info-container {
    padding: 1.5rem;
  }
  
  .product__title h1,
  .product__title h2 {
    font-size: 2rem;
  }
  
  .product-form__buttons {
    flex-direction: column;
  }
  
  .product-form__cart-submit {
    min-width: 100% !important;
  }
}

/* Loading states and micro-interactions */
.product-form__cart-submit.loading {
  position: relative;
  color: transparent !important;
}

.product-form__cart-submit.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced thumbnail gallery */
.thumbnail-list {
  gap: 0.8rem !important;
  margin-top: 1rem !important;
}

.thumbnail-list__item {
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  border: 2px solid transparent !important;
}

.thumbnail-list__item:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.thumbnail-list__item.is-active {
  border-color: #667eea !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.thumbnail-list__item img {
  border-radius: 10px !important;
  transition: all 0.3s ease !important;
}

.thumbnail-list__item:hover img {
  transform: scale(1.05) !important;
}

/* Enhanced product badge styling */
.badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 0.8rem !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3) !important;
}

.badge--sale {
  background: linear-gradient(135deg, #00b894, #00a085) !important;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3) !important;
}

.badge--sold-out {
  background: linear-gradient(135deg, #636e72, #2d3436) !important;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3) !important;
}

/* Enhanced focus states for accessibility */
.product-form__cart-submit:focus-visible,
.share-button:focus-visible,
.quantity__button:focus-visible,
.quantity__input:focus-visible,
.thumbnail-list__item:focus-visible {
  outline: 2px solid #667eea !important;
  outline-offset: 2px !important;
}
