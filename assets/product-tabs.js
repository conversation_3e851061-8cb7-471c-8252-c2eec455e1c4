class ProductTabs {
  constructor() {
    this.init();
  }

  init() {
    this.tabsContainer = document.querySelector('.product-tabs');
    if (!this.tabsContainer) return;

    this.tabs = this.tabsContainer.querySelectorAll('.product-tabs__tab');
    this.panels = this.tabsContainer.querySelectorAll('.product-tabs__panel');

    this.bindEvents();
    this.setInitialState();
  }

  bindEvents() {
    this.tabs.forEach((tab, index) => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        this.switchTab(index);
        // If itinerary tab clicked, try to build mini itinerary once
        const tabName = tab.getAttribute('data-tab');
        if (tabName === 'reviews') {
          this.populateMiniItinerary();
        }
      });

      tab.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.switchTab(index);
          const tabName = tab.getAttribute('data-tab');
          if (tabName === 'reviews') {
            this.populateMiniItinerary();
          }
        }
        
        // Arrow key navigation
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          e.preventDefault();
          const direction = e.key === 'ArrowLeft' ? -1 : 1;
          const visibleTabs = Array.from(this.tabs).filter(tab => 
            tab.style.display !== 'none'
          );
          const currentIndex = visibleTabs.indexOf(tab);
          const nextIndex = (currentIndex + direction + visibleTabs.length) % visibleTabs.length;
          const nextTab = visibleTabs[nextIndex];
          const nextTabIndex = Array.from(this.tabs).indexOf(nextTab);
          this.switchTab(nextTabIndex);
        }
      });
    });

    // Bind show more/less functionality
    this.bindShowMoreButtons();
  }

  switchTab(activeIndex) {
    // Only switch if the tab is visible
    const activeTab = this.tabs[activeIndex];
    if (!activeTab || activeTab.style.display === 'none') return;

    this.tabs.forEach((tab, index) => {
      const isActive = index === activeIndex;
      tab.classList.toggle('active', isActive);
      tab.setAttribute('aria-selected', isActive);
      
      if (isActive) {
        tab.setAttribute('tabindex', '0');
      } else {
        tab.setAttribute('tabindex', '-1');
      }
    });

    this.panels.forEach((panel, index) => {
      const isActive = index === activeIndex;
      panel.classList.toggle('active', isActive);
      panel.setAttribute('aria-hidden', !isActive);
      
      if (isActive) {
        panel.style.display = 'block';
      } else {
        panel.style.display = 'none';
      }
    });

    // Focus the active tab for accessibility
    activeTab.focus();
  }

  setInitialState() {
    // Set ARIA attributes
    this.tabs.forEach((tab, index) => {
      tab.setAttribute('role', 'tab');
      tab.setAttribute('aria-controls', this.panels[index].id);
      tab.setAttribute('tabindex', index === 0 ? '0' : '-1');
    });

    this.panels.forEach((panel, index) => {
      panel.setAttribute('role', 'tabpanel');
      panel.setAttribute('aria-labelledby', this.tabs[index].id || `tab-${index}`);
    });

    // Ensure only Overview tab is visible and active
    this.tabs.forEach((tab, index) => {
      if (index === 0 || tab.getAttribute('data-tab') === 'reviews') {
        tab.classList.toggle('active', index === 0);
        tab.setAttribute('aria-selected', index === 0 ? 'true' : 'false');
        tab.style.display = 'block';
      } else {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
        tab.style.display = 'none';
      }
    });

    this.panels.forEach((panel, index) => {
      if (index === 0 || panel.id === 'reviews') {
        panel.classList.toggle('active', index === 0);
        panel.setAttribute('aria-hidden', index === 0 ? 'false' : 'true');
        panel.style.display = index === 0 ? 'block' : 'none';
      } else {
        panel.classList.remove('active');
        panel.setAttribute('aria-hidden', 'true');
        panel.style.display = 'none';
      }
    });
  }

  bindShowMoreButtons() {
    // Show more functionality for amenities
    const showAllBtn = document.querySelector('.amenities-show-all');
    if (showAllBtn) {
      showAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        // Add functionality to show all amenities
        console.log('Show all amenities clicked');
      });
    }

    // Show more functionality for description
    const showMoreBtn = document.querySelector('.description-show-more');
    if (showMoreBtn) {
      showMoreBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const content = e.target.closest('.description-content');
        if (content) {
          content.classList.toggle('expanded');
          e.target.textContent = content.classList.contains('expanded') 
            ? 'Show less' 
            : 'Show more';
        }
      });
    }

    // View on map functionality
    const viewMapBtn = document.querySelector('.surroundings-view-map');
    if (viewMapBtn) {
      viewMapBtn.addEventListener('click', (e) => {
        e.preventDefault();
        // Add functionality to show map
        console.log('View on map clicked');
      });
    }
  }

  // Method to show specific tabs (can be called externally)
  showTabs(tabNames) {
    this.tabs.forEach((tab, index) => {
      const tabName = tab.getAttribute('data-tab');
      if (tabNames.includes(tabName)) {
        tab.style.display = 'block';
      } else {
        tab.style.display = 'none';
      }
    });
  }

  // Method to show all tabs
  showAllTabs() {
    this.tabs.forEach(tab => {
      tab.style.display = 'block';
    });
  }

  populateMiniItinerary() {
    const container = document.getElementById('mini-itinerary');
    if (!container || container.dataset.built === 'true') return;

    // Look for any AI itinerary sections on the page and extract day titles
    const dayTitleSelectors = [
      '[class*="ai-tour-itinerary__day-title-"]',
      '.itinerary-day .day-title'
    ];
    const titleNodes = dayTitleSelectors
      .flatMap(sel => Array.from(document.querySelectorAll(sel)))
      .filter(Boolean);

    if (titleNodes.length === 0) return;

    const list = document.createElement('ol');
    list.className = 'mini-itinerary__list';

    titleNodes.forEach((node, idx) => {
      const item = document.createElement('li');
      item.className = 'mini-itinerary__item';
      item.textContent = node.textContent.replace(/\s+/g, ' ').trim() || `Day ${idx + 1}`;
      item.addEventListener('click', () => {
        node.scrollIntoView({ behavior: 'smooth', block: 'start' });
      });
      list.appendChild(item);
    });

    container.appendChild(list);
    container.dataset.built = 'true';
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ProductTabs();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProductTabs;
}
