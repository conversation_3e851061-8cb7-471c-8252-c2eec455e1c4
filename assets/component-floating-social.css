/* Floating social bar (right side) */
.floating-social {
  position: fixed;
  right: 1.25rem;
  top: 40vh;
  z-index: 50;
}
.floating-social__list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: #ffffff;
  border: 1px solid rgba(15,23,42,0.08);
  border-radius: 999px;
  padding: 0.5rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.08);
}
.floating-social__list .list-social__link { padding: 0.6rem; }
.floating-social__list .svg-wrapper { display: inline-flex; }

@media (max-width: 990px) {
  .floating-social { display: none; }
}

