/**
 * Product Page Enhancements - Interactive features for FoxyTour products
 */

document.addEventListener('DOMContentLoaded', function() {
  
  // Enhanced image gallery interactions
  function enhanceImageGallery() {
    const mediaItems = document.querySelectorAll('.product__media-item');
    const thumbnails = document.querySelectorAll('.thumbnail-list__item');
    
    // Add smooth transitions to media items
    mediaItems.forEach((item, index) => {
      item.style.transitionDelay = `${index * 0.1}s`;
    });
    
    // Enhanced thumbnail interactions
    thumbnails.forEach((thumbnail, index) => {
      thumbnail.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
      });
      
      thumbnail.addEventListener('mouseleave', function() {
        if (!this.classList.contains('is-active')) {
          this.style.transform = 'translateY(0) scale(1)';
        }
      });
    });
  }
  
  // Enhanced form interactions
  function enhanceFormInteractions() {
    const addToCartButton = document.querySelector('.product-form__cart-submit');
    const quantityInput = document.querySelector('.quantity__input');
    const quantityButtons = document.querySelectorAll('.quantity__button');
    
    // Enhanced add to cart button
    if (addToCartButton) {
      addToCartButton.addEventListener('click', function(e) {
        if (!this.disabled) {
          this.classList.add('loading');
          
          // Remove loading state after form submission
          setTimeout(() => {
            this.classList.remove('loading');
          }, 2000);
        }
      });
    }
    
    // Enhanced quantity controls
    if (quantityInput) {
      quantityInput.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
      });
      
      quantityInput.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
      });
    }
    
    quantityButtons.forEach(button => {
      button.addEventListener('click', function() {
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
          this.style.transform = 'scale(1)';
        }, 150);
      });
    });
  }
  
  // Smooth scroll for product sections
  function enhanceSmoothScroll() {
    const accordionSummaries = document.querySelectorAll('.accordion summary');
    
    accordionSummaries.forEach(summary => {
      summary.addEventListener('click', function() {
        setTimeout(() => {
          this.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
          });
        }, 100);
      });
    });
  }
  
  // Enhanced price display animations
  function enhancePriceDisplay() {
    const priceElements = document.querySelectorAll('.price-item');
    
    priceElements.forEach(price => {
      // Add entrance animation
      price.style.opacity = '0';
      price.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        price.style.transition = 'all 0.6s ease';
        price.style.opacity = '1';
        price.style.transform = 'translateY(0)';
      }, 300);
    });
  }
  
  // Parallax effect for product media (subtle)
  function addParallaxEffect() {
    const mediaWrapper = document.querySelector('.product__media-wrapper');
    
    if (mediaWrapper && window.innerWidth > 768) {
      let ticking = false;
      
      function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallax = scrolled * 0.02;
        
        mediaWrapper.style.transform = `translateY(${parallax}px)`;
        ticking = false;
      }
      
      function requestTick() {
        if (!ticking) {
          requestAnimationFrame(updateParallax);
          ticking = true;
        }
      }
      
      window.addEventListener('scroll', requestTick);
    }
  }
  
  // Enhanced variant selection
  function enhanceVariantSelection() {
    const variantInputs = document.querySelectorAll('input[name="id"]');
    const swatches = document.querySelectorAll('.color-swatch');
    
    swatches.forEach(swatch => {
      swatch.addEventListener('click', function() {
        // Remove selected class from all swatches
        swatches.forEach(s => s.classList.remove('selected'));
        // Add selected class to clicked swatch
        this.classList.add('selected');
        
        // Add ripple effect
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.6)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.left = '50%';
        ripple.style.top = '50%';
        ripple.style.width = '100%';
        ripple.style.height = '100%';
        ripple.style.marginLeft = '-50%';
        ripple.style.marginTop = '-50%';
        
        this.style.position = 'relative';
        this.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });
  }
  
  // Add CSS for ripple animation
  function addRippleAnimation() {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  // Intersection Observer for fade-in animations
  function addScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, observerOptions);
    
    // Observe elements that should animate in
    const animateElements = document.querySelectorAll('.product__info-container > *');
    animateElements.forEach((el, index) => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(30px)';
      el.style.transition = `all 0.6s ease ${index * 0.1}s`;
      observer.observe(el);
    });
  }
  
  // Initialize all enhancements
  function init() {
    enhanceImageGallery();
    enhanceFormInteractions();
    enhanceSmoothScroll();
    enhancePriceDisplay();
    addParallaxEffect();
    enhanceVariantSelection();
    addRippleAnimation();
    addScrollAnimations();
  }
  
  // Run initialization
  init();
  
  // Re-run enhancements when product form updates (for variant changes)
  document.addEventListener('variant:change', init);
});
