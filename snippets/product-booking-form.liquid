{% comment %}
  Renders a booking form for tour products
  
  Accepts:
  - product: {Object} Product object
  
  Usage:
  {% render 'product-booking-form', product: product %}
{% endcomment %}

<div class="booking-form-wrapper">
  <div class="booking-form-container">
    <div class="booking-form-header">
      <div class="booking-price">
        <span class="price-label">From</span>
        <span class="price-amount">{{ product.price_min | money }}</span>
      </div>
    </div>

    <form class="booking-form" id="booking-form-{{ product.id }}" novalidate>
      <!-- Date Selection -->
      <div class="booking-field full-width">
        <label for="booking-date" class="booking-label">{{ 'products.booking.date' | t | default: 'Date' }}</label>
        <input
          type="date"
          id="booking-date"
          name="booking_date"
          class="booking-input"
          required
          min="{{ 'now' | date: '%Y-%m-%d' }}"
          aria-describedby="date-error"
        >
        <div class="field-error" id="date-error" role="alert"></div>
      </div>
      
      <!-- Adults Counter -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.adults' | t | default: 'Adults' }}</label>
        <div class="quantity-selector">
          <button type="button" class="quantity-btn quantity-minus" data-target="adults">-</button>
          <input 
            type="number" 
            id="booking-adults" 
            name="adults" 
            class="quantity-input" 
            value="2" 
            min="1" 
            max="4"
            readonly
          >
          <button type="button" class="quantity-btn quantity-plus" data-target="adults">+</button>
        </div>
        <span class="age-info">{{ 'products.booking.age_18_plus' | t | default: 'Age 18+' }}</span>
      </div>
      
      <!-- Children Counter -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.children' | t | default: 'Children' }}</label>
        <div class="quantity-selector">
          <button type="button" class="quantity-btn quantity-minus" data-target="children">-</button>
          <input 
            type="number" 
            id="booking-children" 
            name="children" 
            class="quantity-input" 
            value="0" 
            min="0" 
            max="4"
            readonly
          >
          <button type="button" class="quantity-btn quantity-plus" data-target="children">+</button>
        </div>
        <span class="age-info">{{ 'products.booking.age_6_17' | t | default: 'Age 6-17' }}</span>
      </div>
      
      <!-- Infants Counter -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.infants' | t | default: 'Infants' }}</label>
        <div class="quantity-selector">
          <button type="button" class="quantity-btn quantity-minus" data-target="infants">-</button>
          <input 
            type="number" 
            id="booking-infants" 
            name="infants" 
            class="quantity-input" 
            value="0" 
            min="0" 
            max="4"
            readonly
          >
          <button type="button" class="quantity-btn quantity-plus" data-target="infants">+</button>
        </div>
        <span class="age-info">{{ 'products.booking.age_0_5' | t | default: 'Age 0-5' }}</span>
      </div>
      
      <!-- Guest Names -->
      <div class="booking-field guest-names full-width" id="guest-names-container">
        <label class="booking-label">{{ 'products.booking.guest_names' | t | default: 'Guest names' }} *</label>
        <div class="guest-name-inputs">
          <div class="guest-name-row">
            <select class="guest-title-select">
              <option value="Mr">{{ 'products.booking.mr' | t | default: 'Mr' }}</option>
              <option value="Ms">{{ 'products.booking.ms' | t | default: 'Ms' }}</option>
              <option value="Mrs">{{ 'products.booking.mrs' | t | default: 'Mrs' }}</option>
            </select>
            <input 
              type="text" 
              class="guest-name-input" 
              placeholder="{{ 'products.booking.guest_name_placeholder' | t | default: 'Guest name' }}"
              required
            >
          </div>
          <div class="guest-name-row">
            <select class="guest-title-select">
              <option value="Mr">{{ 'products.booking.mr' | t | default: 'Mr' }}</option>
              <option value="Ms">{{ 'products.booking.ms' | t | default: 'Ms' }}</option>
              <option value="Mrs">{{ 'products.booking.mrs' | t | default: 'Mrs' }}</option>
            </select>
            <input 
              type="text" 
              class="guest-name-input" 
              placeholder="{{ 'products.booking.guest_name_placeholder' | t | default: 'Guest name' }}"
              required
            >
          </div>
        </div>
      </div>
      
      <!-- Package Selection -->
      

      <!-- Submit Button -->
      <div class="booking-field full-width">
        <button type="submit" class="booking-submit-btn" id="booking-submit-{{ product.id }}">
          <span class="btn-text">{{ 'products.booking.book_now' | t | default: 'Book Now' }}</span>
          <span class="btn-loading" style="display: none;">
            <svg class="loading-spinner" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
              </circle>
            </svg>
            {{ 'products.booking.processing' | t | default: 'Processing...' }}
          </span>
        </button>
        <div class="form-message" id="form-message-{{ product.id }}" role="alert"></div>
      </div>
      
      <!-- Organized by -->
      <div class="booking-footer">
        <span class="organized-by">{{ 'products.booking.organized_by' | t | default: 'Organized by' }}</span>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('booking-form-{{ product.id }}');
  if (!form) return;

  // Enhanced form validation
  const validationRules = {
    'booking-date': {
      required: true,
      validate: (value) => {
        if (!value) return '{{ "products.booking.date_required" | t | default: "Please select a date" }}';
        const selectedDate = new Date(value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (selectedDate < today) return '{{ "products.booking.date_future" | t | default: "Please select a future date" }}';
        return null;
      }
    }
  };

  // Validation helper functions
  function showFieldError(fieldId, message) {
    const field = form.querySelector(`#${fieldId}`).closest('.booking-field');
    const errorDiv = field.querySelector('.field-error');
    field.classList.add('error');
    field.classList.remove('success');
    if (errorDiv) {
      errorDiv.textContent = message;
      errorDiv.classList.add('show');
    }
  }

  function clearFieldError(fieldId) {
    const field = form.querySelector(`#${fieldId}`).closest('.booking-field');
    const errorDiv = field.querySelector('.field-error');
    field.classList.remove('error');
    field.classList.add('success');
    if (errorDiv) {
      errorDiv.classList.remove('show');
    }
  }

  function validateField(fieldId) {
    const input = form.querySelector(`#${fieldId}`);
    const rule = validationRules[fieldId];
    if (!rule) return true;

    const error = rule.validate(input.value);
    if (error) {
      showFieldError(fieldId, error);
      return false;
    } else {
      clearFieldError(fieldId);
      return true;
    }
  }

  // Real-time validation
  Object.keys(validationRules).forEach(fieldId => {
    const input = form.querySelector(`#${fieldId}`);
    if (input) {
      input.addEventListener('blur', () => validateField(fieldId));
      input.addEventListener('input', () => {
        if (input.closest('.booking-field').classList.contains('error')) {
          validateField(fieldId);
        }
      });
    }
  });

  // Enhanced quantity controls with animations
  const quantityBtns = form.querySelectorAll('.quantity-btn');
  quantityBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const target = this.dataset.target;
      const input = form.querySelector(`#booking-${target}`);
      const isPlus = this.classList.contains('quantity-plus');
      const currentValue = parseInt(input.value);
      const max = parseInt(input.max);
      const min = parseInt(input.min);

      let newValue = currentValue;
      if (isPlus && currentValue < max) {
        newValue = currentValue + 1;
      } else if (!isPlus && currentValue > min) {
        newValue = currentValue - 1;
      }

      if (newValue !== currentValue) {
        input.value = newValue;
        input.style.transform = 'scale(1.1)';
        setTimeout(() => input.style.transform = 'scale(1)', 150);
        updateGuestNames();
        checkMaxGuests();
      }
    });
  });
  
  // Update guest name fields based on total guests
  function updateGuestNames() {
    const adults = parseInt(form.querySelector('#booking-adults').value);
    const children = parseInt(form.querySelector('#booking-children').value);
    const totalGuests = adults + children;
    
    const container = form.querySelector('.guest-name-inputs');
    const existingRows = container.querySelectorAll('.guest-name-row');
    
    // Remove excess rows
    for (let i = totalGuests; i < existingRows.length; i++) {
      existingRows[i].remove();
    }
    
    // Add missing rows
    for (let i = existingRows.length; i < totalGuests; i++) {
      const row = document.createElement('div');
      row.className = 'guest-name-row';
      row.innerHTML = `
        <select class="guest-title-select">
          <option value="Mr">{{ 'products.booking.mr' | t | default: 'Mr' }}</option>
          <option value="Ms">{{ 'products.booking.ms' | t | default: 'Ms' }}</option>
          <option value="Mrs">{{ 'products.booking.mrs' | t | default: 'Mrs' }}</option>
        </select>
        <input 
          type="text" 
          class="guest-name-input" 
          placeholder="{{ 'products.booking.guest_name_placeholder' | t | default: 'Guest name' }}"
          required
        >
      `;
      container.appendChild(row);
    }
  }
  
  // Check if total guests exceed 4
  function checkMaxGuests() {
    const adults = parseInt(form.querySelector('#booking-adults').value);
    const children = parseInt(form.querySelector('#booking-children').value);
    const infants = parseInt(form.querySelector('#booking-infants').value);
    const total = adults + children + infants;
    
    // Disable plus buttons if total would exceed 4
    const plusBtns = form.querySelectorAll('.quantity-plus');
    plusBtns.forEach(btn => {
      const target = btn.dataset.target;
      const input = form.querySelector(`#booking-${target}`);
      const currentValue = parseInt(input.value);
      
      if (total >= 4 && target !== 'infants') {
        btn.disabled = true;
      } else if (total >= 4 && target === 'infants' && currentValue === 0) {
        btn.disabled = true;
      } else {
        btn.disabled = false;
      }
    });
  }
  
  // Initialize
  updateGuestNames();
  checkMaxGuests();
  
  // Enhanced form submission with validation and loading states
  form.addEventListener('submit', function(e) {
    e.preventDefault();

    // Validate all fields
    let isValid = true;
    Object.keys(validationRules).forEach(fieldId => {
      if (!validateField(fieldId)) {
        isValid = false;
      }
    });

    // Validate guest names
    const guestInputs = this.querySelectorAll('.guest-name-input');
    const adults = parseInt(form.querySelector('#booking-adults').value);
    const children = parseInt(form.querySelector('#booking-children').value);
    const totalGuests = adults + children;

    let guestNamesValid = true;
    guestInputs.forEach((input, index) => {
      if (index < totalGuests && !input.value.trim()) {
        input.closest('.guest-name-row').style.borderColor = '#ef4444';
        guestNamesValid = false;
      } else {
        input.closest('.guest-name-row').style.borderColor = '';
      }
    });

    if (!guestNamesValid) {
      showFormMessage('{{ "products.booking.guest_names_required" | t | default: "Please enter all guest names" }}', 'error');
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    // Show loading state
    const submitBtn = form.querySelector('#booking-submit-{{ product.id }}');
    const messageDiv = form.querySelector('#form-message-{{ product.id }}');

    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
    messageDiv.className = 'form-message';

    const formData = new FormData(this);
    const bookingData = {
      product_id: {{ product.id }},
      date: formData.get('booking_date'),
      adults: formData.get('adults'),
      children: formData.get('children'),
      infants: formData.get('infants'),
      package: formData.get('package'),
      guests: []
    };

    // Collect guest names
    const guestTitles = this.querySelectorAll('.guest-title-select');

    for (let i = 0; i < guestInputs.length; i++) {
      if (guestInputs[i].value.trim()) {
        bookingData.guests.push({
          title: guestTitles[i].value,
          name: guestInputs[i].value.trim()
        });
      }
    }

    console.log('Booking data:', bookingData);

    // Instead of simulating API call, integrate with contact form
    setTimeout(() => {
      submitBtn.classList.remove('loading');
      submitBtn.disabled = false;

      // Prepare booking data for contact form
      const bookingDetails = formatBookingDetails(bookingData);

      // Show contact form and pre-fill with booking data
      showContactFormWithBookingData(bookingDetails);

      // Show success message
      showFormMessage('{{ "products.booking.please_complete_contact" | t | default: "Please complete your contact information below to finalize your booking." }}', 'info');
    }, 1000);
  });

  // Helper function to show form messages
  function showFormMessage(message, type) {
    const messageDiv = form.querySelector('#form-message-{{ product.id }}');
    messageDiv.textContent = message;
    messageDiv.className = `form-message ${type}`;

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
      setTimeout(() => {
        messageDiv.className = 'form-message';
      }, 5000);
    }
  }

  // Helper function to format booking details for contact form
  function formatBookingDetails(bookingData) {
    const packageNames = {
      'standard': '{{ "products.booking.standard_package" | t | default: "Standard Package" }}',
      'premium': '{{ "products.booking.premium_package" | t | default: "Premium Package" }}',
      'luxury': '{{ "products.booking.luxury_package" | t | default: "Luxury Package" }}'
    };

    let details = `=== {{ "products.booking.booking_details" | t | default: "BOOKING DETAILS" }} ===\n`;
    details += `{{ "products.booking.product" | t | default: "Product" }}: {{ product.title | escape }}\n`;
    details += `{{ "products.booking.date" | t | default: "Date" }}: ${bookingData.date}\n`;
    details += `{{ "products.booking.adults" | t | default: "Adults" }}: ${bookingData.adults}\n`;
    details += `{{ "products.booking.children" | t | default: "Children" }}: ${bookingData.children}\n`;
    details += `{{ "products.booking.infants" | t | default: "Infants" }}: ${bookingData.infants}\n`;
    details += `{{ "products.booking.package" | t | default: "Package" }}: ${packageNames[bookingData.package] || bookingData.package}\n`;

    if (bookingData.guests && bookingData.guests.length > 0) {
      details += `\n{{ "products.booking.guest_names" | t | default: "Guest Names" }}:\n`;
      bookingData.guests.forEach((guest, index) => {
        details += `${index + 1}. ${guest.title} ${guest.name}\n`;
      });
    }

    details += `\n{{ "products.booking.total_guests" | t | default: "Total Guests" }}: ${parseInt(bookingData.adults) + parseInt(bookingData.children) + parseInt(bookingData.infants)}\n`;
    details += `{{ "products.booking.booking_time" | t | default: "Booking Time" }}: ${new Date().toLocaleString()}\n`;

    return details;
  }

  // Helper function to show contact form with booking data
  function showContactFormWithBookingData(bookingDetails) {
    // Find the existing ContactForm on the page
    const contactForm = document.getElementById('ContactForm');
    if (contactForm) {
      // Pre-fill the booking details in the hidden field
      const bookingDataField = contactForm.querySelector('#booking-data');
      if (bookingDataField) {
        bookingDataField.value = bookingDetails;
      }

      // Set the page title
      const pageTitleField = contactForm.querySelector('#page-title');
      if (pageTitleField) {
        pageTitleField.value = document.title;
      }

      // Don't pre-fill the message textarea - let user write their own message
      // Booking details will be sent via hidden field only

      // Scroll to the contact form with smooth animation
      contactForm.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Focus on the first input field after scrolling
      setTimeout(() => {
        const firstInput = contactForm.querySelector('#ContactForm-name');
        if (firstInput && !firstInput.value) {
          firstInput.focus();
        }
      }, 1000);
    } else {
      // If no contact form found, show an alert
      alert('{{ "products.booking.contact_form_not_found" | t | default: "Contact form not found. Please navigate to the contact page to complete your booking." }}');
    }
  }
});
</script>
